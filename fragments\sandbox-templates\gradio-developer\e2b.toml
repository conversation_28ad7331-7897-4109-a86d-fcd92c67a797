# This is a config for E2B sandbox template.
# You can use 'template_id' (1ypi8ae3amtyxttny60k) or 'template_name (gradio-developer) from this config to spawn a sandbox:

# Python SDK
# from e2b import Sandbox
# sandbox = Sandbox(template='gradio-developer')

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create({ template: 'gradio-developer' })

template_id = "1ypi8ae3amtyxttny60k"
dockerfile = "e2b.Dockerfile"
template_name = "gradio-developer"
start_cmd = "cd /home/<USER>"
cpu_count = 4
memory_mb = 4_096
team_id = "460355b3-4f64-48f9-9a16-4442817f79f5"
