# You can use most Debian-based base images
FROM node:21-slim

# Install dependencies and customize sandbox
WORKDIR /home/<USER>/vue-app

RUN npx nuxi@latest init . --packageManager=npm --gitInit=no -f
RUN npx nuxi@latest module add tailwindcss
COPY nuxt.config.ts /home/<USER>/vue-app/nuxt.config.ts

# Move the Vue app to the home directory and remove the Vue directory
RUN mv /home/<USER>/vue-app/* /home/<USER>/ && rm -rf /home/<USER>/vue-app
