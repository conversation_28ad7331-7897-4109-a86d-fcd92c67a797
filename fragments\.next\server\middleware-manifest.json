{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/ssr/_3222cf._.js", "server/edge/chunks/ssr/middleware_ts_8a0420._.js", "server/edge/chunks/ssr/edge-wrapper_bc7dd4.js", "server/edge/chunks/ssr/edge-wrapper_4033f5.js"], "name": "middleware", "page": "/", "matchers": [{"locale": false, "originalSource": "/s/:path*", "regexp": "^/s(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?$"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hVigC2UJuYnCAupH4L8NN/psrVut+7+LnpmHqSsg1z0=", "__NEXT_PREVIEW_MODE_ID": "b5c84c751ddb29b4a863139c35a13bbd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4c029191d1df2eea5988dd2cedcf710f033a828708c0caf3547b592b0a435369", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e3aac41dcb007c0969928044be5a49fe0487f6a30b9084a2c8cc2ad764ede456"}}}, "sortedMiddleware": ["/"], "functions": {}}